{% extends "base.html" %}

{% block title %}Tools - Xapa AI{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>
                    <i class="fas fa-tools me-2"></i>
                    Available Tools
                </h1>
                <div>
                    <button class="btn btn-primary" onclick="refreshTools()">
                        <i class="fas fa-sync me-1"></i>
                        Refresh Tools
                    </button>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <input type="text" class="form-control" id="searchTools" 
                                   placeholder="Search tools..." onkeyup="filterTools()">
                        </div>
                        <div class="col-md-6">
                            <select class="form-select" id="categoryFilter" onchange="filterTools()">
                                <option value="">All Categories</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tools Grid -->
            <div id="toolsGrid" class="row g-4">
                <!-- Tools will be loaded here -->
            </div>

            <!-- No tools message -->
            <div id="noToolsMessage" class="text-center py-5" style="display: none;">
                <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No tools found</h4>
                <p class="text-muted">Try adjusting your search criteria or refresh the tools list.</p>
            </div>
        </div>
    </div>
</div>

<!-- Tool Details Modal -->
<div class="modal fade" id="toolModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="toolModalTitle">Tool Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="toolModalBody">
                <!-- Tool details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Loading tools...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let allTools = [];
let filteredTools = [];

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadTools();

    // Fallback: Force close loading modal after 10 seconds if still visible
    setTimeout(() => {
        const modal = document.getElementById('loadingModal');
        if (modal && modal.classList.contains('show')) {
            console.warn('Force closing loading modal after timeout');
            utils.forceCloseDialog('loadingModal');
        }
    }, 10000);
});

// Load tools from API
async function loadTools() {
    let loadingTimeout;

    try {
        console.log('Starting to load tools...');
        utils.showLoading(true, 'Loading tools...');

        // Set a backup timeout to force close loading modal
        loadingTimeout = setTimeout(() => {
            console.warn('Loading timeout reached, force closing modal');
            utils.forceCloseDialog('loadingModal');
        }, 15000); // Increased to 15 seconds for API calls

        console.log('Making API request to /tools...');
        const response = await utils.apiGet('/tools');
        console.log('Tools response received:', response);

        // Clear the timeout since we got a response
        clearTimeout(loadingTimeout);

        // Handle different response formats
        if (response && response.tools) {
            allTools = response.tools;
            console.log('Using response.tools, count:', allTools.length);
        } else if (Array.isArray(response)) {
            allTools = response;
            console.log('Using response as array, count:', allTools.length);
        } else {
            console.warn('Unexpected response format:', response);
            allTools = [];
        }

        filteredTools = [...allTools];

        console.log('Populating categories...');
        populateCategories();

        console.log('Rendering tools...');
        renderTools();

        console.log('Tools loaded successfully, total count:', allTools.length);

    } catch (error) {
        console.error('Failed to load tools:', error);
        clearTimeout(loadingTimeout);
        utils.showAlert('Failed to load tools: ' + error.message, 'danger');

        // Set empty arrays as fallback
        allTools = [];
        filteredTools = [];
        populateCategories();
        renderTools();
    } finally {
        console.log('Finally block: Hiding loading modal...');
        utils.showLoading(false);
    }
}

// Populate category filter
function populateCategories() {
    const categoryFilter = document.getElementById('categoryFilter');
    const categories = [...new Set(allTools.map(tool => tool.category))].sort();
    
    // Clear existing options except "All Categories"
    categoryFilter.innerHTML = '<option value="">All Categories</option>';
    
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category.charAt(0).toUpperCase() + category.slice(1);
        categoryFilter.appendChild(option);
    });
}

// Render tools grid
function renderTools() {
    const grid = document.getElementById('toolsGrid');
    const noToolsMessage = document.getElementById('noToolsMessage');
    
    if (filteredTools.length === 0) {
        grid.innerHTML = '';
        noToolsMessage.style.display = 'block';
        return;
    }
    
    noToolsMessage.style.display = 'none';
    
    const toolsHtml = filteredTools.map(tool => `
        <div class="col-md-6 col-lg-4">
            <div class="card h-100 tool-card" onclick="showToolDetails('${tool.name}')">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="card-title mb-0">${utils.escapeHtml(tool.name)}</h6>
                        <span class="badge bg-secondary">${utils.escapeHtml(tool.category)}</span>
                    </div>
                    <p class="card-text text-muted small">
                        ${utils.truncateText(tool.description, 100)}
                    </p>
                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-cog me-1"></i>
                                ${tool.required.length} required params
                            </small>
                            <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); showToolDetails('${tool.name}')">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    grid.innerHTML = toolsHtml;
}

// Filter tools
function filterTools() {
    const searchTerm = document.getElementById('searchTools').value.toLowerCase();
    const selectedCategory = document.getElementById('categoryFilter').value;
    
    filteredTools = allTools.filter(tool => {
        const matchesSearch = !searchTerm || 
            tool.name.toLowerCase().includes(searchTerm) ||
            tool.description.toLowerCase().includes(searchTerm) ||
            tool.category.toLowerCase().includes(searchTerm);
        
        const matchesCategory = !selectedCategory || tool.category === selectedCategory;
        
        return matchesSearch && matchesCategory;
    });
    
    renderTools();
}

// Show tool details
function showToolDetails(toolName) {
    const tool = allTools.find(t => t.name === toolName);
    if (!tool) return;
    
    const modal = document.getElementById('toolModal');
    const title = document.getElementById('toolModalTitle');
    const body = document.getElementById('toolModalBody');
    
    title.textContent = tool.name;
    
    body.innerHTML = `
        <div class="mb-3">
            <h6>Description</h6>
            <p>${utils.escapeHtml(tool.description)}</p>
        </div>
        
        <div class="mb-3">
            <h6>Category</h6>
            <span class="badge bg-secondary">${utils.escapeHtml(tool.category)}</span>
        </div>
        
        <div class="mb-3">
            <h6>Required Parameters</h6>
            ${tool.required.length > 0 ? 
                `<ul class="list-unstyled">
                    ${tool.required.map(param => `<li><code>${utils.escapeHtml(param)}</code></li>`).join('')}
                </ul>` :
                '<p class="text-muted">No required parameters</p>'
            }
        </div>
        
        <div class="mb-3">
            <h6>Schema</h6>
            <pre class="bg-light p-3 rounded"><code>${JSON.stringify(tool.schema, null, 2)}</code></pre>
        </div>
    `;
    
    new bootstrap.Modal(modal).show();
}

// Refresh tools
async function refreshTools() {
    await loadTools();
    setTimeout(() => {
        utils.showAlert('Tools refreshed successfully', 'success', 2000);
    }, 500); // Delay to ensure modal is gone
}

// Add CSS for tool cards
const style = document.createElement('style');
style.textContent = `
    .tool-card {
        cursor: pointer;
        transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .tool-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
