from fastapi import Depends, Request, HTTPException, status
from fastapi.responses import RedirectResponse
from fastapi.security import OAuth2P<PERSON>wordBearer
from sqlalchemy.orm import Session
from typing import Optional
import uuid
from datetime import timedelta

from app.models.base import get_db
from app.models.user import User
from app.core.security import verify_token, create_access_token
from app.core.exceptions import AuthenticationError

# OAuth2 scheme for token authentication (made optional for anonymous access)
# Note: tokenUrl is not used since we accept external JWT tokens and anonymous tokens
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/anonymous", auto_error=False)


async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> User:
    """
    Get current authenticated user from JWT token.
    If the user does not exist, create a new one.
    
    Args:
        token: JWT token from request header
        db: Database session
        
    Returns:
        User: Authenticated user object
        
    Raises:
        AuthenticationError: If token is invalid or user not found
    """
    # Verify token
    payload = verify_token(token)
    if not payload:
        raise AuthenticationError("Invalid authentication token")
    
    # Extract user ID from token
    user_id_str = payload.get("sub")
    if not user_id_str:
        raise AuthenticationError("Invalid token payload")
    
    try:
        user_id = uuid.UUID(user_id_str)
    except ValueError:
        raise AuthenticationError("Invalid user ID in token")
    
    # Get user from database
    user = db.query(User).filter(User.id == user_id).first()
    
    if not user:
        # User does not exist, create a new one
        new_user = User(
            id=user_id,
            username=f"user_{user_id_str[:8]}",
            email=f"{user_id_str}@example.com",
            password_hash="not_set", # User created via token, no password
            is_active=True
        )
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        return new_user

    if not user.is_active:
        raise AuthenticationError("User account is disabled")
    
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current active user (additional check for user status).
    
    Args:
        current_user: Current user from get_current_user
        
    Returns:
        User: Active user object
        
    Raises:
        AuthenticationError: If user is not active
    """
    if not current_user.is_active:
        raise AuthenticationError("User account is disabled")
    
    return current_user


def create_anonymous_token() -> str:
    """
    Create an anonymous access token for temporary users.

    Returns:
        str: JWT token for anonymous user
    """
    # Generate a random UUID for anonymous user
    anonymous_id = str(uuid.uuid4())

    # Create token with shorter expiration for anonymous users (1 hour)
    access_token_expires = timedelta(hours=1)
    access_token = create_access_token(
        data={"sub": anonymous_id, "anonymous": True},
        expires_delta=access_token_expires
    )

    return access_token


async def get_anonymous_user(
    token: Optional[str] = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> User:
    """
    Get current user or create anonymous user from token.
    This allows the application to work with anonymous authentication.

    Args:
        token: JWT token from request header (required for anonymous users too)
        db: Database session

    Returns:
        User: Authenticated user or anonymous user object

    Raises:
        AuthenticationError: If no token provided
    """
    if not token:
        raise AuthenticationError("Authentication token required")

    # Verify token and get user ID
    payload = verify_token(token)
    if not payload:
        raise AuthenticationError("Invalid authentication token")

    user_id_str = payload.get("sub")
    if not user_id_str:
        raise AuthenticationError("Invalid token payload")

    try:
        user_id = uuid.UUID(user_id_str)
    except ValueError:
        raise AuthenticationError("Invalid user ID in token")

    # Check if this is an anonymous token
    is_anonymous = payload.get("anonymous", False)

    if is_anonymous:
        # Check if anonymous user already exists in database
        existing_user = db.query(User).filter(User.id == user_id).first()
        if existing_user:
            return existing_user

        # Create new anonymous user in database
        anonymous_user = User(
            id=user_id,
            username=f"anonymous_{str(user_id)[:8]}",
            email=f"anonymous_{str(user_id)}@temp.local",
            password_hash="anonymous",
            is_active=True
        )

        db.add(anonymous_user)
        db.commit()
        db.refresh(anonymous_user)

        return anonymous_user

    # For regular users, get from database
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise AuthenticationError("User not found")

    if not user.is_active:
        raise AuthenticationError("User account is disabled")

    return user


# Web Authentication Dependencies (for Auth0)

async def get_current_web_user(
    request: Request,
    db: Session = Depends(get_db)
) -> Optional[User]:
    """
    Get current authenticated user from web session (Auth0).
    This is used for web interface authentication.

    Args:
        request: FastAPI request object containing session
        db: Database session

    Returns:
        Optional[User]: Authenticated user object or None if not authenticated
    """
    # Check if user is authenticated via session
    user_info = request.session.get('user')
    if not user_info:
        return None
    
    print(f"User info from session: {user_info}")

    user_email = user_info.get('email')
    if not user_email:
        return None

    try:
        # For Auth0 users, use the full Auth0 ID as a string identifier
        # We'll need to find or create user by Auth0 ID
        user = db.query(User).filter(User.email == user_email).first()

        if not user:
            # Create new user from Auth0 profile
            user = User(
                id=uuid.uuid4(),  # Generate new UUID for our system
                username=user_info.get('nickname', user_info.get('name', f"user_{uuid.uuid4().hex[:8]}")),
                email=user_email,
                password_hash="auth0_user",  # Auth0 users don't have local passwords
                is_active=True
            )
            db.add(user)
            db.commit()
            db.refresh(user)

        return user

    except (ValueError, TypeError):
        return None
