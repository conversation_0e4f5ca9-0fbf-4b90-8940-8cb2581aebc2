"""
Auth0 authentication service for web interface.
Handles OAuth2 flows, token validation, and user profile management.
"""

import json
import secrets
from typing import Optional, Dict, Any
from urllib.parse import urlencode, quote_plus
import requests
from authlib.integrations.requests_client import OAuth2Session
from authlib.jose import jwt
from authlib.jose.errors import JoseError

from app.core.config import settings
from app.core.exceptions import AuthenticationError

# Allowed email domains for login
ALLOWED_DOMAINS = ["xapa.com", "stanford.edu"]


class Auth0Service:
    """Service for handling Auth0 authentication."""
    
    def __init__(self):
        """Initialize Auth0 service with configuration."""
        if not all([settings.auth0_domain, settings.auth0_client_id, settings.auth0_client_secret]):
            raise ValueError("Auth0 configuration is incomplete. Please set AUTH0_DOMAIN, AUTH0_CLIENT_ID, and AUTH0_CLIENT_SECRET")
        
        self.domain = settings.auth0_domain
        self.client_id = settings.auth0_client_id
        self.client_secret = settings.auth0_client_secret
        self.callback_url = settings.auth0_callback_url
        self.audience = settings.auth0_audience
        
        # OAuth2 endpoints
        self.authorization_endpoint = f"https://{self.domain}/authorize"
        self.token_endpoint = f"https://{self.domain}/oauth/token"
        self.userinfo_endpoint = f"https://{self.domain}/userinfo"
    
    def get_authorization_url(self, state: Optional[str] = None) -> tuple[str, str]:
        """
        Generate Auth0 authorization URL for login.

        Args:
            state: Optional state parameter for CSRF protection

        Returns:
            Tuple of (authorization_url, state)
        """
        if not state:
            state = secrets.token_urlsafe(32)

        params = {
            'response_type': 'code',
            'client_id': self.client_id,
            'redirect_uri': self.callback_url,
            'scope': 'openid profile email',
            'state': state
        }

        if self.audience:
            params['audience'] = self.audience

        auth_url = f"{self.authorization_endpoint}?" + urlencode(params)
        return auth_url, state
    
    def exchange_code_for_tokens(self, code: str, state: str) -> Dict[str, Any]:
        """
        Exchange authorization code for tokens.
        
        Args:
            code: Authorization code from Auth0 callback
            state: State parameter for CSRF validation
            
        Returns:
            Token response containing access_token, id_token, etc.
            
        Raises:
            AuthenticationError: If token exchange fails
        """
        token_url = self.token_endpoint
        
        payload = {
            'grant_type': 'authorization_code',
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'code': code,
            'redirect_uri': self.callback_url
        }
        
        headers = {'content-type': 'application/x-www-form-urlencoded'}
        
        try:
            response = requests.post(token_url, data=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            raise AuthenticationError(f"Failed to exchange code for tokens: {str(e)}")
    
    def get_user_info(self, access_token: str) -> Dict[str, Any]:
        """
        Get user information from Auth0 using access token.

        Args:
            access_token: Auth0 access token

        Returns:
            User profile information

        Raises:
            AuthenticationError: If user info retrieval fails or domain not allowed
        """
        userinfo_url = self.userinfo_endpoint
        headers = {'Authorization': f'Bearer {access_token}'}

        try:
            response = requests.get(userinfo_url, headers=headers)
            response.raise_for_status()
            user_info = response.json()

            # Check if user's email domain is allowed
            email = user_info.get('email', '')
            if not self.is_domain_allowed(email):
                raise AuthenticationError(f"Access denied. Only users from {', '.join(ALLOWED_DOMAINS)} domains are allowed.")

            return user_info
        except requests.RequestException as e:
            raise AuthenticationError(f"Failed to get user info: {str(e)}")

    def is_domain_allowed(self, email: str) -> bool:
        """
        Check if the email domain is in the allowed domains list.

        Args:
            email: User's email address

        Returns:
            bool: True if domain is allowed, False otherwise
        """
        if not email or '@' not in email:
            return False

        domain = email.split('@')[1].lower()
        return domain in ALLOWED_DOMAINS
    
    def validate_id_token(self, id_token: str) -> Dict[str, Any]:
        """
        Validate and decode Auth0 ID token.
        
        Args:
            id_token: JWT ID token from Auth0
            
        Returns:
            Decoded token payload
            
        Raises:
            AuthenticationError: If token validation fails
        """
        try:
            # Get Auth0 public keys
            jwks_url = f"https://{self.domain}/.well-known/jwks.json"
            jwks_response = requests.get(jwks_url)
            jwks_response.raise_for_status()
            jwks = jwks_response.json()
            
            # Decode and validate token
            claims = jwt.decode(
                id_token,
                jwks,
                claims_options={
                    'iss': {'essential': True, 'value': f'https://{self.domain}/'},
                    'aud': {'essential': True, 'value': self.client_id}
                }
            )
            
            return claims
        except (JoseError, requests.RequestException) as e:
            raise AuthenticationError(f"Invalid ID token: {str(e)}")
    
    def get_logout_url(self, return_to: Optional[str] = None) -> str:
        """
        Generate Auth0 logout URL.
        
        Args:
            return_to: URL to redirect to after logout
            
        Returns:
            Auth0 logout URL
        """
        params = {'client_id': self.client_id}
        
        if return_to:
            params['returnTo'] = return_to
        
        return f"https://{self.domain}/v2/logout?" + urlencode(params)


# Global Auth0 service instance
def get_auth0_service() -> Optional[Auth0Service]:
    """
    Get Auth0 service instance if configured.
    
    Returns:
        Auth0Service instance or None if not configured
    """
    try:
        return Auth0Service()
    except ValueError:
        # Auth0 not configured, return None
        return None


# Initialize service if configured
auth0_service = get_auth0_service()
