from fastapi import APIRouter, Request, Depends, HTTPException, status
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jin<PERSON>2Templates
from sqlalchemy.orm import Session
from typing import Optional

from app.models.base import get_db
from app.models.role import Role
from app.models.user import User
from app.core.dependencies import get_current_web_user

router = APIRouter()
templates = Jinja2Templates(directory="templates")


@router.get("/", response_class=HTMLResponse)
async def home(
    request: Request, 
    current_user: Optional[User] = Depends(get_current_web_user)
):
    """Home page. Redirect to login if not authenticated."""
    if not current_user:
        return RedirectResponse(url="/auth/login", status_code=302)

    return templates.TemplateResponse("home.html", {
        "request": request,
        "current_user": current_user
    })


@router.get("/roles", response_class=HTMLResponse)
async def roles_list(
    request: Request, 
    current_user: Optional[User] = Depends(get_current_web_user)
):
    """Roles management page. Requires authentication."""
    if not current_user:
        return RedirectResponse(url="/auth/login", status_code=302)

    return templates.TemplateResponse("roles/list.html", {
        "request": request,
        "current_user": current_user
    })


@router.get("/roles/create", response_class=HTMLResponse)
async def create_role_page(
    request: Request, 
    current_user: Optional[User] = Depends(get_current_web_user),
):
    """Create role page. Requires authentication."""
    if not current_user:
        return RedirectResponse(url="/auth/login", status_code=302)

    return templates.TemplateResponse("roles/form.html", {
        "request": request,
        "role": None,
        "current_user": current_user
    })


@router.get("/roles/{role_name}/edit", response_class=HTMLResponse)
async def edit_role_page(
    request: Request,
    role_name: str,
    current_user: Optional[User] = Depends(get_current_web_user),
    db: Session = Depends(get_db)
):
    """Edit role page. Requires authentication."""
    if not current_user:
        return RedirectResponse(url="/auth/login", status_code=302)
    role = db.query(Role).filter(Role.name == role_name).first()

    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Role '{role_name}' not found"
        )
    
    # Convert role to dict for template
    role_dict = {
        "id": str(role.id),
        "name": role.name,
        "display_name": role.display_name,
        "description": role.description,
        "system_prompt": role.system_prompt,
        "tools": role.tools or [],
        "config": role.config or {},
        "is_active": role.is_active,
        "created_at": role.created_at.isoformat() if role.created_at else None,
        "updated_at": role.updated_at.isoformat() if role.updated_at else None
    }
    
    return templates.TemplateResponse("roles/form.html", {
        "request": request,
        "role": role_dict,
        "current_user": current_user
    })


@router.get("/tools", response_class=HTMLResponse)
async def tools_page(
    request: Request, 
    current_user: Optional[User] = Depends(get_current_web_user)
):
    """Tools management page. Requires authentication."""
    if not current_user:
        return RedirectResponse(url="/auth/login", status_code=302)

    return templates.TemplateResponse("tools/list.html", {
        "request": request,
        "current_user": current_user
    })


@router.get("/test", response_class=HTMLResponse)
async def test_agent_page(
    request: Request,
    role: Optional[str] = None,
    current_user: Optional[User] = Depends(get_current_web_user)
):
    """AI agent testing page. Anonymous access allowed."""
    if not current_user:
        return RedirectResponse(url="/auth/login", status_code=302)

    return templates.TemplateResponse("test/agent.html", {
        "request": request,
        "selected_role": role,
        "current_user": current_user
    })


@router.get("/health", response_class=HTMLResponse)
async def health_page(
    request: Request, 
    current_user: Optional[User] = Depends(get_current_web_user)
):
    """System health page. Anonymous access allowed."""
    return templates.TemplateResponse("admin/health.html", {
        "request": request,
        "current_user": current_user
    })
