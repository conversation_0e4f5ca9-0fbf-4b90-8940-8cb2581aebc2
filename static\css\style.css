/* Custom styles for Xapa AI */

:root {
    --primary-color: #6705bd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.navbar-brand {
    font-weight: 600;
}

.navbar-custom-primary {
    background-color: var(--primary-color) !important;
}

.bg-custom-primary {
    background-color: var(--primary-color) !important;
}

/* Card Enhancements */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Tool Selection Styles */
.tool-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: all 0.15s ease-in-out;
    cursor: pointer;
}

.tool-item:hover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.tool-item.selected {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.1);
}

.tool-category {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background-color: rgba(13, 110, 253, 0.1);
    border-radius: 0.25rem;
}

.tool-name {
    font-weight: 600;
    color: var(--dark-color);
}

.tool-description {
    color: var(--secondary-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.tool-parameters {
    font-size: 0.75rem;
    color: var(--info-color);
    margin-top: 0.5rem;
}

/* Status Badges */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.status-active {
    background-color: var(--success-color);
    color: white;
}

.status-inactive {
    background-color: var(--secondary-color);
    color: white;
}

/* Table Enhancements */
.table th {
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Button Enhancements */
.btn {
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn-sm {
    font-size: 0.8rem;
}

/* Form Enhancements */
.form-label {
    font-weight: 600;
    color: var(--dark-color);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-text {
    font-size: 0.8rem;
    color: var(--secondary-color);
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: 0.5rem;
}

/* Chat Interface Styles */
.chat-container {
    height: 500px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    background-color: white;
}

.message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: 0.5rem;
    max-width: 80%;
}

.message.user {
    background-color: var(--primary-color);
    color: white;
    margin-left: auto;
    text-align: right;
}

.message.assistant {
    background-color: var(--light-color);
    color: var(--dark-color);
    margin-right: auto;
}

.message.system {
    background-color: var(--warning-color);
    color: var(--dark-color);
    margin: 0 auto;
    text-align: center;
    font-style: italic;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-top: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        font-size: 0.875rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

.border-dashed {
    border-style: dashed !important;
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .table {
        color: #ffffff;
    }
    
    .form-control {
        background-color: #2d2d2d;
        border-color: #495057;
        color: #ffffff;
    }
}