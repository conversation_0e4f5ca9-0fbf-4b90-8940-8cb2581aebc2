# Application
APP_NAME=XAPA_AI
APP_ENV=development
DEBUG=true

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/agent_db
REDIS_URL=redis://localhost:6379/0

# External User Database (for user lookup tools)
# Set this to connect to your external PostgreSQL database containing user information
EXTERNAL_USER_DB_URL=postgresql://username:password@localhost:5432/company_users

# LLM Provider Configuration
LLM_PROVIDER=azure_openai

# OpenAI (Alternative Provider)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# Azure OpenAI (Alternative Provider)
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com/
AZURE_OPENAI_API_VERSION=2025-01-01-preview
AZURE_OPENAI_MODEL=your_model_name

# Embedding Provider Configuration
EMBEDDING_PROVIDER=azure_openai

# OpenAI Embeddings
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# Azure OpenAI Embeddings
AZURE_OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# Pinecone
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_INDEX_NAME=your_pinecone_index_name_here

# Security
SECRET_KEY=your-secret-key-here-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_HOURS=24

# Auth0 Configuration (for web authentication)
# Set these to enable Auth0 login for the web interface
# API authentication will continue to work with JWT tokens
AUTH0_DOMAIN=your-auth0-domain.auth0.com
AUTH0_CLIENT_ID=your_auth0_client_id
AUTH0_CLIENT_SECRET=your_auth0_client_secret
AUTH0_CALLBACK_URL=http://localhost:8000/auth/callback
AUTH0_AUDIENCE=your_auth0_api_identifier

# Limits
MAX_SESSIONS_PER_USER=10
MAX_MESSAGES_PER_SESSION=1000
MAX_MESSAGE_LENGTH=4000